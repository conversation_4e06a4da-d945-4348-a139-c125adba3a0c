<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mobile Drag Test - Billboard Maker</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #f0f0f0;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .test-canvas {
            width: 100%;
            height: 400px;
            background: #fff;
            border: 2px solid #ddd;
            position: relative;
            margin: 20px 0;
        }
        
        .test-element {
            position: absolute;
            width: 150px;
            height: 80px;
            background: linear-gradient(45deg, #007cba, #005a87);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 8px;
            cursor: move;
            user-select: none;
            touch-action: none;
            -webkit-touch-callout: none;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
            transition: transform 0.1s ease;
        }
        
        .test-element:hover {
            transform: scale(1.02);
        }
        
        .test-element.dragging {
            transform: scale(1.05);
            z-index: 1000;
            box-shadow: 0 4px 16px rgba(0,0,0,0.3);
        }
        
        .instructions {
            background: #e7f3ff;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid #007cba;
        }
        
        .status {
            background: #f0f8ff;
            padding: 10px;
            border-radius: 4px;
            margin-top: 20px;
            font-family: monospace;
            font-size: 14px;
        }
        
        .button-group {
            margin: 20px 0;
        }
        
        .test-button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        
        .test-button:hover {
            background: #005a87;
        }
        
        .test-button:active {
            transform: scale(0.98);
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔧 Mobile Drag & Drop Test</h1>
        
        <div class="instructions">
            <h3>📱 Test Instructions:</h3>
            <ol>
                <li><strong>Desktop:</strong> Click and drag the blue element</li>
                <li><strong>Mobile:</strong> Touch and drag the blue element</li>
                <li><strong>After dragging:</strong> Try clicking the buttons below to verify elements remain clickable</li>
                <li><strong>Expected:</strong> Element should move smoothly and buttons should remain clickable after drag</li>
            </ol>
        </div>
        
        <div class="button-group">
            <button class="test-button" onclick="testClick('Button 1')">Test Button 1</button>
            <button class="test-button" onclick="testClick('Button 2')">Test Button 2</button>
            <button class="test-button" onclick="resetElement()">Reset Position</button>
            <button class="test-button" onclick="addElement()">Add Element</button>
        </div>
        
        <div class="test-canvas" id="testCanvas">
            <div class="test-element" id="testElement1" style="top: 50px; left: 50px;">
                Drag Me! 🎯
            </div>
        </div>
        
        <div class="status" id="status">
            Status: Ready for testing. Try dragging the element above.
        </div>
    </div>

    <script>
        let dragData = {
            isDragging: false,
            startX: 0,
            startY: 0,
            elementX: 0,
            elementY: 0,
            element: null
        };
        
        let elementCounter = 1;
        
        function updateStatus(message) {
            document.getElementById('status').textContent = `Status: ${message}`;
        }
        
        function testClick(buttonName) {
            updateStatus(`${buttonName} clicked successfully! Elements are clickable.`);
        }
        
        function resetElement() {
            const element = document.getElementById('testElement1');
            if (element) {
                element.style.left = '50px';
                element.style.top = '50px';
                updateStatus('Element position reset.');
            }
        }
        
        function addElement() {
            elementCounter++;
            const canvas = document.getElementById('testCanvas');
            const newElement = document.createElement('div');
            newElement.className = 'test-element';
            newElement.id = `testElement${elementCounter}`;
            newElement.style.left = `${100 + (elementCounter * 20)}px`;
            newElement.style.top = `${100 + (elementCounter * 20)}px`;
            newElement.textContent = `Element ${elementCounter} 🎯`;
            
            setupElementEvents(newElement);
            canvas.appendChild(newElement);
            updateStatus(`Added new element ${elementCounter}`);
        }
        
        function setupElementEvents(element) {
            // Mouse events
            element.addEventListener('mousedown', (e) => handleStart(e, element, 'mouse'));
            
            // Touch events
            element.addEventListener('touchstart', (e) => handleStart(e, element, 'touch'), { passive: false });
        }
        
        function handleStart(e, element, type) {
            e.preventDefault();
            
            const clientX = type === 'touch' ? e.touches[0].clientX : e.clientX;
            const clientY = type === 'touch' ? e.touches[0].clientY : e.clientY;
            
            dragData.isDragging = true;
            dragData.startX = clientX;
            dragData.startY = clientY;
            dragData.elementX = parseInt(element.style.left) || 0;
            dragData.elementY = parseInt(element.style.top) || 0;
            dragData.element = element;
            
            element.classList.add('dragging');
            updateStatus(`Started dragging with ${type} input`);
        }
        
        function handleMove(e, type) {
            if (!dragData.isDragging || !dragData.element) return;
            
            e.preventDefault();
            
            const clientX = type === 'touch' ? e.touches[0].clientX : e.clientX;
            const clientY = type === 'touch' ? e.touches[0].clientY : e.clientY;
            
            const deltaX = clientX - dragData.startX;
            const deltaY = clientY - dragData.startY;
            
            const newX = dragData.elementX + deltaX;
            const newY = dragData.elementY + deltaY;
            
            // Keep within canvas bounds
            const canvas = document.getElementById('testCanvas');
            const canvasRect = canvas.getBoundingClientRect();
            const elementRect = dragData.element.getBoundingClientRect();
            
            const maxX = canvasRect.width - elementRect.width;
            const maxY = canvasRect.height - elementRect.height;
            
            dragData.element.style.left = Math.max(0, Math.min(newX, maxX)) + 'px';
            dragData.element.style.top = Math.max(0, Math.min(newY, maxY)) + 'px';
        }
        
        function handleEnd(e, type) {
            if (!dragData.isDragging) return;
            
            if (dragData.element) {
                dragData.element.classList.remove('dragging');
            }
            
            dragData.isDragging = false;
            dragData.element = null;
            
            updateStatus(`Finished dragging with ${type} input. Try clicking buttons to test clickability.`);
        }
        
        // Global event listeners
        document.addEventListener('mousemove', (e) => handleMove(e, 'mouse'));
        document.addEventListener('mouseup', (e) => handleEnd(e, 'mouse'));
        
        document.addEventListener('touchmove', (e) => handleMove(e, 'touch'), { passive: false });
        document.addEventListener('touchend', (e) => handleEnd(e, 'touch'));
        document.addEventListener('touchcancel', (e) => handleEnd(e, 'touch'));
        
        // Initialize first element
        setupElementEvents(document.getElementById('testElement1'));
        
        updateStatus('Test loaded. Try dragging the element above.');
    </script>
</body>
</html>
